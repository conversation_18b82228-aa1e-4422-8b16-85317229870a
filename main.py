from manim import *
import numpy as np
import random

class FibonacciOrganicIllusion(MovingCameraScene):
    def construct(self):
        # Enhanced parameters for more beauty
        total_points = 1000
        golden_angle = 2 * np.pi * (1 - 1 / ((1 + 5 ** 0.5) / 2))

        # Keep original black background (Manim default)
        self.camera.background_color = BLACK

        dots = VGroup()
        petals = VGroup()

        # Step 1: Enhanced Spiral Dots with dynamic colors
        for i in range(total_points):
            theta = i * golden_angle
            r = np.sqrt(i) * 0.05
            x = r * np.cos(theta)
            y = r * np.sin(theta)

            # Dynamic color based on position and index - enhanced for black background
            hue = (i / total_points + np.sin(theta) * 0.1) % 1
            color_choices = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE, PINK, GOLD, WHITE, "#00FFFF", "#FF00FF", "#FFFF00"]
            color = color_choices[int(hue * len(color_choices))]

            dot = Dot(
                point=[x, y, 0],
                radius=0.015 + 0.005 * np.sin(i * 0.1),
                color=color,
                fill_opacity=1.0,
                stroke_width=1,
                stroke_color=WHITE,
                stroke_opacity=0.3
            )
            dots.add(dot)

        # Animate dots with wave effect
        self.play(
            LaggedStart(*[
                AnimationGroup(
                    FadeIn(dot, scale=0.3),
                    dot.animate.set_fill(opacity=1.0)
                ) for dot in dots
            ], lag_ratio=0.0005),
            run_time=3
        )
        self.wait(0.5)

        # Step 2: Magical Petal Connections with multiple layers
        petal_layers = []

        # Create multiple petal layers with different skip values
        for layer_idx, skip in enumerate([21, 34, 55, 89]):
            layer_petals = VGroup()

            for i in range(total_points - skip):
                p1 = dots[i].get_center()
                p2 = dots[i + skip].get_center()

                # Create vibrant rainbow gradient optimized for black background
                progress = i / (total_points - skip)
                hue = (progress * 3 + layer_idx * 0.25) % 1
                rainbow_colors = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE, PINK, GOLD, WHITE, "#00FFFF", "#FF00FF", "#FFFF00"]
                color = rainbow_colors[int(hue * len(rainbow_colors)) % len(rainbow_colors)]

                # Vary line thickness for organic feel
                thickness = 1.0 + 0.5 * np.sin(i * 0.05 + layer_idx)

                line = Line(
                    p1, p2,
                    color=color,
                    stroke_width=thickness,
                    stroke_opacity=0.8 - layer_idx * 0.1
                )
                layer_petals.add(line)

            petal_layers.append(layer_petals)
            petals.add(layer_petals)

        # Animate each layer with different timing
        for i, layer in enumerate(petal_layers):
            self.play(
                LaggedStart(*[
                    AnimationGroup(
                        Create(line),
                        line.animate.set_stroke(opacity=0.8)
                    ) for line in layer
                ], lag_ratio=0.0008),
                run_time=3 + i * 0.5
            )
            self.wait(0.2)

        # Add pulsating effect to petals
        self.play(
            *[
                AnimationGroup(*[
                    line.animate.set_stroke(opacity=0.9).set_stroke(width=line.stroke_width * 1.2)
                    for line in layer
                ]) for layer in petal_layers
            ],
            run_time=1.5,
            rate_func=there_and_back
        )

        # Step 3: Focus purely on the spiral beauty

        # Step 4: Focus on the central spiral

        # Step 5: Spectacular zoom and rotation finale focused on the spiral
        self.play(
            AnimationGroup(
                self.camera.frame.animate.scale(0.6).move_to(dots[0].get_center()),
                Rotate(dots, angle=PI/6, about_point=dots[0].get_center()),
                Rotate(petals, angle=PI/8, about_point=dots[0].get_center())
            ),
            run_time=4,
            rate_func=smooth
        )

        # Final magical transformation with vibrant colors
        self.play(
            *[
                dot.animate.set_fill(
                    color=random.choice([GOLD, PINK, BLUE, WHITE, GREEN, ORANGE, "#00FFFF", "#FF00FF", YELLOW]),
                    opacity=1.0
                ).scale(random.uniform(0.8, 1.3))
                for dot in dots[::10]  # Every 10th dot for performance
            ],
            run_time=2
        )

        # Hold the beautiful result
        self.wait(4)
