from manim import *
import numpy as np

class FibonacciOrganicIllusion(MovingCameraScene):
    def construct(self):
        # Parameters for realistic sunflower spiral
        total_points = 1000
        golden_angle = 2 * np.pi * (1 - 1 / ((1 + 5 ** 0.5) / 2))

        # Clean black background
        self.camera.background_color = BLACK

        dots = VGroup()
        petals = VGroup()

        # Phase 1: Simple dot plotting with clean white/light dots
        for i in range(total_points):
            theta = i * golden_angle
            r = np.sqrt(i) * 0.05  # scaling factor to fit screen
            x = r * np.cos(theta)
            y = r * np.sin(theta)

            # Start with simple light-colored dots
            dot = Dot(
                point=[x, y, 0],
                radius=0.012 + 0.003 * np.sin(i * 0.1),  # Slight size variation
                color=WHITE,
                fill_opacity=0.0,  # Start invisible
                stroke_width=0.5,
                stroke_color=LIGHT_GRAY,
                stroke_opacity=0.0  # Start invisible
            )
            dots.add(dot)

        # Organic dot appearance animation - show dots appearing in spiral order
        batch_size = 40  # Smaller batches for smoother organic feel
        for batch_start in range(0, total_points, batch_size):
            batch_end = min(batch_start + batch_size, total_points)
            batch_dots = dots[batch_start:batch_end]

            self.play(
                LaggedStart(*[
                    AnimationGroup(
                        FadeIn(dot, scale=0.2),
                        dot.animate.set_fill(opacity=0.8).set_stroke(opacity=0.3)
                    ) for dot in batch_dots
                ], lag_ratio=0.015),
                run_time=0.6,
                rate_func=smooth
            )

        # Brief pause to appreciate the complete spiral pattern
        self.wait(1.5)

        # Phase 2: Connection animation - elegant line drawing
        petal_layers = []

        # Create multiple petal layers with Fibonacci skip values
        fibonacci_skips = [21, 34, 55, 89]
        for layer_idx, skip in enumerate(fibonacci_skips):
            layer_petals = VGroup()

            for i in range(total_points - skip):
                p1 = dots[i].get_center()
                p2 = dots[i + skip].get_center()

                # Subtle, elegant line colors that complement the dots
                if layer_idx == 0:
                    color = LIGHT_GRAY
                elif layer_idx == 1:
                    color = GRAY
                elif layer_idx == 2:
                    color = "#C0C0C0"  # Silver
                else:
                    color = "#A0A0A0"  # Darker gray

                # Consistent line thickness with slight variation
                thickness = 0.8 + 0.2 * np.sin(i * 0.03)

                line = Line(
                    p1, p2,
                    color=color,
                    stroke_width=thickness,
                    stroke_opacity=0.0  # Start invisible
                )
                layer_petals.add(line)

            petal_layers.append(layer_petals)
            petals.add(layer_petals)

        # Animate each connection layer appearing sequentially
        for i, layer in enumerate(petal_layers):
            self.play(
                LaggedStart(*[
                    line.animate.set_stroke(opacity=0.6 - i * 0.1)
                    for line in layer
                ], lag_ratio=0.001),
                run_time=2.5,
                rate_func=smooth
            )
            self.wait(0.3)

        # Brief pause to appreciate the connected pattern
        self.wait(1)

        # Phase 3: Realistic sunflower coloring transformation

        def get_sunflower_color(dot_index, total_dots):
            """Generate realistic sunflower colors based on radial position"""
            # Calculate distance from center (normalized)
            distance_ratio = np.sqrt(dot_index / total_dots)

            if distance_ratio < 0.15:  # Inner center - dark brown/deep yellow
                return "#8B4513"  # Saddle brown
            elif distance_ratio < 0.35:  # Inner ring - rich brown
                return "#A0522D"  # Sienna
            elif distance_ratio < 0.55:  # Middle ring - golden brown
                return "#CD853F"  # Peru
            elif distance_ratio < 0.75:  # Outer middle - warm golden yellow
                return "#DAA520"  # Goldenrod
            else:  # Outer petals - bright golden yellow
                return "#FFD700"  # Gold

        # Apply sunflower colors gradually from center outward
        color_batches = []
        batch_size = 50

        for batch_start in range(0, total_points, batch_size):
            batch_end = min(batch_start + batch_size, total_points)
            batch_dots = dots[batch_start:batch_end]

            # Create color animations for this batch
            batch_animations = []
            for i, dot in enumerate(batch_dots):
                dot_index = batch_start + i
                sunflower_color = get_sunflower_color(dot_index, total_points)

                batch_animations.append(
                    dot.animate.set_fill(color=sunflower_color, opacity=0.9)
                )

            color_batches.append(batch_animations)

        # Animate color transformation in waves from center to outer edge
        for i, batch_animations in enumerate(color_batches):
            self.play(
                *batch_animations,
                run_time=0.4,
                rate_func=smooth
            )

        # Update connection lines to complement the sunflower colors
        sunflower_line_colors = ["#CD853F", "#DAA520", "#B8860B", "#9ACD32"]  # Natural browns and golds

        for layer_idx, layer in enumerate(petal_layers):
            line_color = sunflower_line_colors[layer_idx % len(sunflower_line_colors)]
            self.play(
                *[line.animate.set_stroke(color=line_color, opacity=0.4)
                  for line in layer],
                run_time=1.0,
                rate_func=smooth
            )

        # Final phase: Gentle showcase of the natural sunflower pattern

        # Subtle zoom to highlight the mathematical beauty
        self.play(
            self.camera.frame.animate.scale(0.7).move_to(dots[0].get_center()),
            run_time=3,
            rate_func=smooth
        )

        # Gentle breathing effect to show the pattern is alive
        self.play(
            *[
                dot.animate.scale(1.05).set_fill(opacity=1.0)
                for dot in dots[::25]  # Every 25th dot for subtle effect
            ],
            run_time=2,
            rate_func=there_and_back
        )

        # Hold the beautiful natural sunflower result
        self.wait(3)

        # Gentle zoom out to appreciate the complete pattern
        self.play(
            self.camera.frame.animate.scale(1.3),
            run_time=2,
            rate_func=smooth
        )

        # Final hold to appreciate the mathematical beauty of nature
        self.wait(3)
