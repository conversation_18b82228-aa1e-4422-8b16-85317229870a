from manim import *
import numpy as np
import random

class FibonacciOrganicIllusion(MovingCameraScene):
    def construct(self):
        # Enhanced parameters for more beauty
        total_points = 1000
        golden_angle = 2 * np.pi * (1 - 1 / ((1 + 5 ** 0.5) / 2))

        # Keep original black background (Man<PERSON> default)
        self.camera.background_color = BLACK

        dots = VGroup()
        petals = VGroup()
        glow_circles = VGroup()
        sparkles = VGroup()

        # Create magical sparkle particles
        for _ in range(200):
            sparkle_pos = [
                random.uniform(-8, 8),
                random.uniform(-6, 6),
                0
            ]
            sparkle = Star(
                n=4,
                outer_radius=0.02,
                inner_radius=0.01,
                color=random.choice([YELLOW, WHITE, GOLD, PINK, BLUE, GREEN, RED, ORANGE]),
                fill_opacity=random.uniform(0.5, 1.0)
            ).move_to(sparkle_pos)
            sparkles.add(sparkle)

        # Animate sparkles twinkling
        self.play(
            LaggedStart(*[
                FadeIn(sparkle, scale=0.1) for sparkle in sparkles
            ], lag_ratio=0.01),
            run_time=2
        )

        # Step 1: Enhanced Spiral Dots with dynamic colors
        for i in range(total_points):
            theta = i * golden_angle
            r = np.sqrt(i) * 0.05
            x = r * np.cos(theta)
            y = r * np.sin(theta)

            # Dynamic color based on position and index - enhanced for black background
            hue = (i / total_points + np.sin(theta) * 0.1) % 1
            color_choices = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE, PINK, GOLD, WHITE, "#00FFFF", "#FF00FF", "#FFFF00"]
            color = color_choices[int(hue * len(color_choices))]

            dot = Dot(
                point=[x, y, 0],
                radius=0.015 + 0.005 * np.sin(i * 0.1),
                color=color,
                fill_opacity=1.0,
                stroke_width=1,
                stroke_color=WHITE,
                stroke_opacity=0.3
            )
            dots.add(dot)

        # Animate dots with wave effect
        self.play(
            LaggedStart(*[
                AnimationGroup(
                    FadeIn(dot, scale=0.3),
                    dot.animate.set_fill(opacity=1.0)
                ) for dot in dots
            ], lag_ratio=0.0005),
            run_time=3
        )
        self.wait(0.5)

        # Step 2: Magical Petal Connections with multiple layers
        petal_layers = []

        # Create multiple petal layers with different skip values
        for layer_idx, skip in enumerate([21, 34, 55, 89]):
            layer_petals = VGroup()

            for i in range(total_points - skip):
                p1 = dots[i].get_center()
                p2 = dots[i + skip].get_center()

                # Create vibrant rainbow gradient optimized for black background
                progress = i / (total_points - skip)
                hue = (progress * 3 + layer_idx * 0.25) % 1
                rainbow_colors = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE, PINK, GOLD, WHITE, "#00FFFF", "#FF00FF", "#FFFF00"]
                color = rainbow_colors[int(hue * len(rainbow_colors)) % len(rainbow_colors)]

                # Vary line thickness for organic feel
                thickness = 1.0 + 0.5 * np.sin(i * 0.05 + layer_idx)

                line = Line(
                    p1, p2,
                    color=color,
                    stroke_width=thickness,
                    stroke_opacity=0.8 - layer_idx * 0.1
                )
                layer_petals.add(line)

            petal_layers.append(layer_petals)
            petals.add(layer_petals)

        # Animate each layer with different timing
        for i, layer in enumerate(petal_layers):
            self.play(
                LaggedStart(*[
                    AnimationGroup(
                        Create(line),
                        line.animate.set_stroke(opacity=0.8)
                    ) for line in layer
                ], lag_ratio=0.0008),
                run_time=3 + i * 0.5
            )
            self.wait(0.2)

        # Add pulsating effect to petals
        self.play(
            *[
                AnimationGroup(*[
                    line.animate.set_stroke(opacity=0.9).set_stroke(width=line.stroke_width * 1.2)
                    for line in layer
                ]) for layer in petal_layers
            ],
            run_time=1.5,
            rate_func=there_and_back
        )

        # Step 3: Magical glowing rings with vibrant colors for black background
        ring_colors = [GOLD, PINK, "#00FFFF", PURPLE, GREEN, ORANGE, WHITE, YELLOW]
        for i, radius in enumerate(np.linspace(0.3, 4.0, 8)):
            color = ring_colors[i % len(ring_colors)]
            ring = Circle(
                radius=radius,
                color=color,
                stroke_opacity=0.4,
                stroke_width=4,
                fill_opacity=0.08,
                fill_color=color
            )
            glow_circles.add(ring)

        glow_circles.move_to(dots[0].get_center())

        # Animate rings with ripple effect
        self.play(
            LaggedStart(*[
                AnimationGroup(
                    FadeIn(ring, scale=0.1),
                    ring.animate.set_stroke(opacity=0.4)
                ) for ring in glow_circles
            ], lag_ratio=0.3),
            run_time=3
        )

        # Add pulsating glow animation
        self.play(
            *[
                ring.animate.set_stroke(opacity=0.6).scale(1.1)
                for ring in glow_circles
            ],
            run_time=2,
            rate_func=there_and_back_with_pause
        )

        # Step 4: Create floating energy orbs
        orbs = VGroup()
        for _ in range(50):
            angle = random.uniform(0, 2 * np.pi)
            distance = random.uniform(1, 3)
            x = distance * np.cos(angle)
            y = distance * np.sin(angle)

            orb = Circle(
                radius=random.uniform(0.02, 0.05),
                color=random.choice([YELLOW, PINK, BLUE, WHITE, GREEN, ORANGE, "#00FFFF", "#FF00FF"]),
                fill_opacity=0.9,
                stroke_width=1,
                stroke_color=WHITE,
                stroke_opacity=0.5
            ).move_to([x, y, 0])
            orbs.add(orb)

        self.play(
            LaggedStart(*[
                FadeIn(orb, scale=0.1) for orb in orbs
            ], lag_ratio=0.05),
            run_time=2
        )

        # Animate orbs floating
        self.play(
            *[
                orb.animate.shift([
                    random.uniform(-0.5, 0.5),
                    random.uniform(-0.5, 0.5),
                    0
                ]).set_fill(opacity=random.uniform(0.3, 1.0))
                for orb in orbs
            ],
            run_time=3,
            rate_func=smooth
        )

        # Step 5: Spectacular zoom and rotation finale
        self.play(
            AnimationGroup(
                self.camera.frame.animate.scale(0.6).move_to(dots[0].get_center()),
                Rotate(dots, angle=PI/6, about_point=dots[0].get_center()),
                Rotate(petals, angle=PI/8, about_point=dots[0].get_center()),
                *[
                    sparkle.animate.rotate(PI/4).set_fill(opacity=random.uniform(0.5, 1.0))
                    for sparkle in sparkles
                ]
            ),
            run_time=4,
            rate_func=smooth
        )

        # Final magical transformation with vibrant colors
        self.play(
            *[
                dot.animate.set_fill(
                    color=random.choice([GOLD, PINK, BLUE, WHITE, GREEN, ORANGE, "#00FFFF", "#FF00FF", YELLOW]),
                    opacity=1.0
                ).scale(random.uniform(0.8, 1.3))
                for dot in dots[::10]  # Every 10th dot for performance
            ],
            run_time=2
        )

        # Hold the beautiful result
        self.wait(4)
