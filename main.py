from manim import *
import numpy as np
import random

class FibonacciOrganicIllusion(MovingCameraScene):
    def construct(self):
        # Enhanced parameters for organic beauty
        total_points = 1000  # Optimized for fast evaluation
        golden_angle = 2 * np.pi * (1 - 1 / ((1 + 5 ** 0.5) / 2))

        # Keep clean black background for focus
        self.camera.background_color = BLACK

        dots = VGroup()
        petals = VGroup()

        # Step 1: Organic dot plotting - create dots first, then animate them appearing
        for i in range(total_points):
            theta = i * golden_angle
            r = np.sqrt(i) * 0.05  # scaling factor to fit screen
            x = r * np.cos(theta)
            y = r * np.sin(theta)

            # Dynamic color system for vibrant appearance on black background
            hue = (i / total_points + np.sin(theta) * 0.1) % 1
            color_choices = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE, PINK, GOLD, WHITE, "#00FFFF", "#FF00FF", "#FFFF00"]
            color = color_choices[int(hue * len(color_choices))]

            dot = Dot(
                point=[x, y, 0],
                radius=0.015 + 0.005 * np.sin(i * 0.1),  # Organic size variation
                color=color,
                fill_opacity=0.0,  # Start invisible
                stroke_width=1,
                stroke_color=WHITE,
                stroke_opacity=0.0  # Start invisible
            )
            dots.add(dot)

        # Step 2: Organic appearance animation - dots appear in batches with wave effect
        batch_size = 50  # Show dots in batches for organic feel
        for batch_start in range(0, total_points, batch_size):
            batch_end = min(batch_start + batch_size, total_points)
            batch_dots = dots[batch_start:batch_end]

            self.play(
                LaggedStart(*[
                    AnimationGroup(
                        FadeIn(dot, scale=0.3),
                        dot.animate.set_fill(opacity=1.0).set_stroke(opacity=0.3)
                    ) for dot in batch_dots
                ], lag_ratio=0.02),
                run_time=0.8,
                rate_func=smooth
            )

        # Brief pause to appreciate the spiral pattern
        self.wait(1)

        # Step 3: Organic petal connection phase - connect dots to form beautiful patterns
        petal_layers = []

        # Create multiple petal layers with different skip values
        for layer_idx, skip in enumerate([21, 34, 55, 89]):
            layer_petals = VGroup()

            for i in range(total_points - skip):
                p1 = dots[i].get_center()
                p2 = dots[i + skip].get_center()

                # Create vibrant rainbow gradient optimized for black background
                progress = i / (total_points - skip)
                hue = (progress * 3 + layer_idx * 0.25) % 1
                rainbow_colors = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE, PINK, GOLD, WHITE, "#00FFFF", "#FF00FF", "#FFFF00"]
                color = rainbow_colors[int(hue * len(rainbow_colors)) % len(rainbow_colors)]

                # Vary line thickness for organic feel
                thickness = 1.0 + 0.5 * np.sin(i * 0.05 + layer_idx)

                line = Line(
                    p1, p2,
                    color=color,
                    stroke_width=thickness,
                    stroke_opacity=0.8 - layer_idx * 0.1
                )
                layer_petals.add(line)

            petal_layers.append(layer_petals)
            petals.add(layer_petals)

        # Animate each layer with different timing
        for i, layer in enumerate(petal_layers):
            self.play(
                LaggedStart(*[
                    AnimationGroup(
                        Create(line),
                        line.animate.set_stroke(opacity=0.8)
                    ) for line in layer
                ], lag_ratio=0.0008),
                run_time=3 + i * 0.5
            )
            self.wait(0.2)

        # Step 4: Organic highlighting phase - make the spiral feel alive and attractive

        # Pulsating effect on petals for organic beauty
        self.play(
            *[
                AnimationGroup(*[
                    line.animate.set_stroke(opacity=0.9).set_stroke(width=line.stroke_width * 1.2)
                    for line in layer
                ]) for layer in petal_layers
            ],
            run_time=1.5,
            rate_func=there_and_back
        )

        # Organic dot highlighting - create breathing effect
        self.play(
            *[
                dot.animate.scale(1.3).set_fill(opacity=0.95)
                for dot in dots[::30]  # Every 30th dot for organic rhythm
            ],
            run_time=2.0,
            rate_func=there_and_back_with_pause
        )

        # Wave effect across the spiral
        self.play(
            LaggedStart(*[
                dot.animate.set_fill(
                    color=random.choice([GOLD, WHITE, "#00FFFF", PINK, YELLOW]),
                    opacity=1.0
                ).scale(1.1)
                for dot in dots[::15]  # Every 15th dot
            ], lag_ratio=0.02),
            run_time=3,
            rate_func=smooth
        )

        # Step 5: Classic dramatic finale - zoom and rotation to showcase the mathematical beauty
        self.play(
            AnimationGroup(
                self.camera.frame.animate.scale(0.5).move_to(dots[0].get_center()),
                Rotate(dots, angle=PI/4, about_point=dots[0].get_center()),
                Rotate(petals, angle=PI/6, about_point=dots[0].get_center()),
                *[
                    dot.animate.set_fill(opacity=1.0).scale(1.1)
                    for dot in dots[::20]  # Highlight key dots during rotation
                ]
            ),
            run_time=5,
            rate_func=smooth
        )

        # Final transformation - classic mathematical beauty
        finale_colors = [GOLD, WHITE, "#00FFFF", PINK, YELLOW, GREEN, ORANGE]
        self.play(
            LaggedStart(*[
                dot.animate.set_fill(
                    color=random.choice(finale_colors),
                    opacity=1.0
                ).scale(random.uniform(0.9, 1.2))
                for dot in dots[::8]  # Every 8th dot for rich detail
            ], lag_ratio=0.01),
            run_time=3,
            rate_func=smooth
        )

        # Hold the beautiful mathematical result
        self.wait(3)

        # Gentle fade to appreciate the complete pattern
        self.play(
            self.camera.frame.animate.scale(1.2),
            run_time=2,
            rate_func=smooth
        )
        self.wait(2)
